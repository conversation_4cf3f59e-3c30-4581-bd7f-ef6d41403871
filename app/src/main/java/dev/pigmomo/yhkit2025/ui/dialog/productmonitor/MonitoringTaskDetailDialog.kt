package dev.pigmomo.yhkit2025.ui.dialog.productmonitor

import androidx.compose.foundation.background
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringOperationType
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringPlanEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringType
import dev.pigmomo.yhkit2025.service.productmonitor.MonitoringServiceManager
import dev.pigmomo.yhkit2025.service.productmonitor.MonitoringTaskResult
import dev.pigmomo.yhkit2025.ui.theme.cardThemeOverlay
import dev.pigmomo.yhkit2025.ui.theme.dialogContainerColor
import dev.pigmomo.yhkit2025.utils.productmonitor.MonitoringStatusUtils
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

/**
 * 监控任务详情弹窗
 * @param plan 监控计划
 * @param onDismiss 关闭弹窗回调
 * @param onEdit 编辑任务回调
 * @param onDelete 删除任务回调
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MonitoringTaskDetailDialog(
    plan: MonitoringPlanEntity,
    onDismiss: () -> Unit,
    onEdit: (MonitoringPlanEntity) -> Unit,
    onDelete: (MonitoringPlanEntity) -> Unit
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val dateFormat = remember { SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()) }
    
    // 状态管理
    var isExecuting by remember { mutableStateOf(false) }
    var executionResult by remember { mutableStateOf<MonitoringTaskResult?>(null) }
    var showDeleteConfirm by remember { mutableStateOf(false) }
    
    // 获取监控状态
    var monitoringStatus by remember { mutableStateOf(MonitoringStatusUtils.getMonitoringStatus(plan)) }
    
    // 定时更新状态
    LaunchedEffect(plan.id, monitoringStatus.isWaiting) {
        if (monitoringStatus.isWaiting) {
            while (true) {
                delay(1000)
                monitoringStatus = MonitoringStatusUtils.getMonitoringStatus(plan)
            }
        }
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        confirmButton = {},
        title = {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "任务详情",
                    modifier = Modifier.weight(1f)
                )
                
                // 操作按钮
                Row {
                    // 编辑按钮
                    IconButton(onClick = { onEdit(plan) }) {
                        Icon(
                            Icons.Default.Edit,
                            contentDescription = "编辑",
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }
                    
                    // 删除按钮
                    IconButton(onClick = { showDeleteConfirm = true }) {
                        Icon(
                            Icons.Default.Delete,
                            contentDescription = "删除",
                            tint = Color.Red
                        )
                    }
                }
            }
        },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(max = 600.dp)
                    .verticalScroll(rememberScrollState()),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // 基本信息
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = cardThemeOverlay()
                ) {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Text(
                            text = "基本信息",
                            fontWeight = FontWeight.Bold,
                            fontSize = 16.sp
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        DetailRow("任务名称", plan.name)
                        DetailRow("监控类型", when (plan.type) {
                            MonitoringType.CART -> "购物车监控"
                            MonitoringType.PRODUCT_DETAIL -> "商品详情监控"
                        })
                        DetailRow("操作类型", when (plan.operationType) {
                            MonitoringOperationType.INTERVAL -> "间隔监控"
                            MonitoringOperationType.SCHEDULED -> "定时监控"
                            MonitoringOperationType.MANUAL -> "手动监控"
                        })
                        DetailRow("任务状态", if (plan.isEnabled) "启用" else "禁用")
                        DetailRow("优先级", plan.priority.toString())
                    }
                }

                // 执行状态
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = cardThemeOverlay()
                ) {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Text(
                            text = "执行状态",
                            fontWeight = FontWeight.Bold,
                            fontSize = 16.sp
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Box(
                                modifier = Modifier
                                    .size(12.dp)
                                    .background(
                                        color = monitoringStatus.statusColor,
                                        shape = CircleShape
                                    )
                            )
                            
                            Spacer(modifier = Modifier.width(8.dp))
                            
                            Text(
                                text = monitoringStatus.statusText,
                                color = monitoringStatus.statusColor,
                                fontWeight = FontWeight.Medium
                            )
                        }
                        
                        if (monitoringStatus.nextExecutionTime != null) {
                            Spacer(modifier = Modifier.height(4.dp))
                            DetailRow("下次执行", MonitoringStatusUtils.formatNextExecutionTime(monitoringStatus.nextExecutionTime))
                        }
                        
                        DetailRow("已执行次数", "${plan.executedCount}")
                        if (plan.maxExecutions != -1) {
                            DetailRow("最大执行次数", "${plan.maxExecutions}")
                        }
                        
                        plan.lastExecutedAt?.let {
                            DetailRow("最后执行时间", dateFormat.format(it))
                        }
                    }
                }

                // 配置信息
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = cardThemeOverlay()
                ) {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Text(
                            text = "配置信息",
                            fontWeight = FontWeight.Bold,
                            fontSize = 16.sp
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        if (plan.operationType == MonitoringOperationType.INTERVAL) {
                            DetailRow("监控间隔", "${plan.intervalSeconds}秒")
                        }
                        
                        if (plan.operationType == MonitoringOperationType.SCHEDULED && plan.scheduledConfig.isNotEmpty()) {
                            DetailRow("定时配置", plan.scheduledConfig)
                        }
                        
                        DetailRow("商品数量", "${plan.productIds.size}")
                        if (plan.productIds.isNotEmpty()) {
                            DetailRow("商品ID", plan.productIds.joinToString(", "))
                        }
                        
                        DetailRow("任务账号", plan.account.phoneNumber)
                        DetailRow("服务类型", plan.serviceType)
                        
                        if (plan.note.isNotEmpty()) {
                            DetailRow("任务备注", plan.note)
                        }
                        
                        DetailRow("创建时间", dateFormat.format(plan.createdAt))
                    }
                }

                // 执行结果
                executionResult?.let { result ->
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = if (result.success) Color.Green.copy(alpha = 0.1f) else Color.Red.copy(alpha = 0.1f)
                        )
                    ) {
                        Column(modifier = Modifier.padding(16.dp)) {
                            Text(
                                text = if (result.success) "✓ 执行成功" else "✗ 执行失败",
                                fontWeight = FontWeight.Bold,
                                color = if (result.success) Color.Green else Color.Red
                            )
                            
                            Spacer(modifier = Modifier.height(8.dp))
                            
                            Text(
                                text = result.message,
                                fontSize = 14.sp,
                                color = Color.Gray
                            )
                            
                            if (result.success && (result.changesDetected > 0 || result.importantChanges > 0)) {
                                Spacer(modifier = Modifier.height(4.dp))
                                Text(
                                    text = "检测到 ${result.changesDetected} 个变化 (${result.importantChanges} 个重要)",
                                    fontSize = 14.sp,
                                    color = Color.Blue
                                )
                            }
                        }
                    }
                }
            }
        },
        containerColor = dialogContainerColor()
    )

    // 删除确认对话框
    if (showDeleteConfirm) {
        AlertDialog(
            onDismissRequest = { showDeleteConfirm = false },
            title = { Text("确认删除") },
            text = { Text("确定要删除监控任务 \"${plan.name}\" 吗？此操作不可撤销。") },
            confirmButton = {
                TextButton(
                    onClick = {
                        onDelete(plan)
                        showDeleteConfirm = false
                        onDismiss()
                    }
                ) {
                    Text("删除", color = Color.Red)
                }
            },
            dismissButton = {
                TextButton(onClick = { showDeleteConfirm = false }) {
                    Text("取消")
                }
            }
        )
    }
}

@Composable
private fun DetailRow(label: String, value: String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 2.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = "$label:",
            fontSize = 14.sp,
            color = Color.Gray,
            modifier = Modifier.weight(1.5f),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )
        Text(
            text = value,
            fontSize = 14.sp,
            modifier = Modifier.weight(2f).horizontalScroll(rememberScrollState())
        )
    }
}
