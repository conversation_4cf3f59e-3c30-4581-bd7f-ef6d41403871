package dev.pigmomo.yhkit2025.service.productmonitor

import android.util.Log
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringOperationType
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringPlanEntity
import dev.pigmomo.yhkit2025.data.repository.productmonitor.MonitoringPlanRepository
import dev.pigmomo.yhkit2025.utils.productmonitor.MonitoringScheduleUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import java.util.Date

/**
 * 监控调度服务
 * 负责管理和调度监控任务的执行
 */
class MonitoringSchedulerService(
    private val monitoringPlanRepository: MonitoringPlanRepository,
    private val monitoringTaskExecutor: MonitoringTaskExecutor
) {

    companion object {
        private const val TAG = "MonitoringSchedulerService"
        private const val SCHEDULER_INTERVAL = 60_000L // 1分钟检查一次
    }

    private val schedulerScope = CoroutineScope(Dispatchers.Default + SupervisorJob())
    private var isRunning = false

    /**
     * 启动监控调度器
     */
    fun startScheduler() {
        if (isRunning) {
            Log.w(TAG, "监控调度器已经在运行中")
            return
        }

        Log.i(TAG, "启动监控调度器")
        isRunning = true

        // 发送调度器状态变化事件
        MonitoringEventBus.tryEmit(MonitoringEventBus.MonitoringEvent.SchedulerStatusChanged(true))

        schedulerScope.launch {
            while (isActive && isRunning) {
                try {
                    // 执行调度检查
                    performScheduleCheck()

                    // 等待下次检查
                    delay(SCHEDULER_INTERVAL)

                } catch (e: Exception) {
                    Log.e(TAG, "监控调度器执行异常", e)
                    // 发生异常时等待更长时间再重试
                    delay(SCHEDULER_INTERVAL * 2)
                }
            }
        }
    }

    /**
     * 停止监控调度器
     */
    fun stopScheduler() {
        Log.i(TAG, "停止监控调度器")
        isRunning = false

        // 发送调度器状态变化事件
        MonitoringEventBus.tryEmit(MonitoringEventBus.MonitoringEvent.SchedulerStatusChanged(false))
    }

    /**
     * 执行调度检查
     */
    private suspend fun performScheduleCheck() {
        Log.d(TAG, "执行监控调度检查 - 调度器运行状态: $isRunning")

        try {
            // 获取所有启用的监控计划，然后在内存中进行详细判断
            val allEnabledPlans = monitoringPlanRepository.getEnabledMonitoringPlans().first()
            Log.d(TAG, "获取到 ${allEnabledPlans.size} 个启用的监控计划")

            // 分别处理不同类型的监控计划
            val intervalPlans = allEnabledPlans.filter { it.operationType == MonitoringOperationType.INTERVAL }
            val scheduledPlans = allEnabledPlans.filter { it.operationType == MonitoringOperationType.SCHEDULED }

            Log.d(TAG, "间隔监控计划总数: ${intervalPlans.size}, 定时监控计划总数: ${scheduledPlans.size}")

            // 使用统一的shouldExecute逻辑进行判断
            val intervalPlansToExecute = intervalPlans
                .filter {
                    val shouldExecute = MonitoringScheduleUtils.shouldExecute(it)
                    Log.d(TAG, "间隔监控计划 ${it.name} 执行检查结果: $shouldExecute (上次执行: ${it.lastExecutedAt}, 间隔: ${it.intervalSeconds}秒)")
                    shouldExecute
                }
                .sortedByDescending { it.priority }

            val scheduledPlansToExecute = scheduledPlans
                .filter {
                    val shouldExecute = MonitoringScheduleUtils.shouldExecute(it)
                    Log.d(TAG, "定时监控计划 ${it.name} 执行检查结果: $shouldExecute (上次执行: ${it.lastExecutedAt}, 配置: ${it.scheduledConfig})")
                    shouldExecute
                }
                .sortedByDescending { it.priority }

            // 合并所有需要执行的计划
            val allPlansToExecute = (intervalPlansToExecute + scheduledPlansToExecute)
                .sortedByDescending { it.priority }

            Log.d(TAG, "间隔监控待执行: ${intervalPlansToExecute.size}, 定时监控待执行: ${scheduledPlansToExecute.size}")
            Log.d(TAG, "总计待执行任务: ${allPlansToExecute.size}")
            allPlansToExecute.forEach { plan ->
                Log.d(TAG, "待执行任务: ${plan.name}, ID: ${plan.id}, 类型: ${plan.operationType}")
            }

            if (allPlansToExecute.isNotEmpty()) {
                Log.i(TAG, "开始执行 ${allPlansToExecute.size} 个监控计划")

                // 执行监控任务
                val results = monitoringTaskExecutor.executeBatchTasks(allPlansToExecute)

                // 统计执行结果
                val successCount = results.count { it.second.success }
                val failureCount = results.size - successCount
                val totalChanges = results.sumOf { it.second.changesDetected }
                val totalImportantChanges = results.sumOf { it.second.importantChanges }

                Log.i(TAG, "监控任务执行完成: 成功 $successCount, 失败 $failureCount, 检测到 $totalChanges 个变化 (其中 $totalImportantChanges 个重要变化)")

                // 记录失败的任务
                results.filter { !it.second.success }.forEach { (plan, result) ->
                    Log.w(TAG, "监控计划 ${plan.name} 执行失败: ${result.message}")
                }

            } else {
                Log.d(TAG, "当前没有需要执行的监控计划")
            }

        } catch (e: Exception) {
            Log.e(TAG, "执行调度检查时发生异常", e)
        }
    }

    /**
     * 手动执行指定的监控计划
     * @param planId 监控计划ID
     * @return 执行结果
     */
    suspend fun executeManualTask(planId: Int): MonitoringTaskResult {
        Log.d(TAG, "手动执行监控计划: $planId")

        return try {
            val plan = monitoringPlanRepository.getMonitoringPlanById(planId)

            if (plan != null) {
                if (plan.isEnabled) {
                    monitoringTaskExecutor.executeTask(plan)
                } else {
                    MonitoringTaskResult(
                        success = false,
                        message = "监控计划已禁用"
                    )
                }
            } else {
                MonitoringTaskResult(
                    success = false,
                    message = "监控计划不存在"
                )
            }

        } catch (e: Exception) {
            Log.e(TAG, "手动执行监控计划时发生异常", e)
            MonitoringTaskResult(
                success = false,
                message = "执行异常: ${e.message}"
            )
        }
    }

    /**
     * 执行指定类型的所有监控计划
     * @param operationType 监控操作类型
     * @return 执行结果列表
     */
    suspend fun executeTasksByType(operationType: MonitoringOperationType): List<Pair<MonitoringPlanEntity, MonitoringTaskResult>> {
        Log.d(TAG, "执行指定类型的监控计划: $operationType")

        return try {
            val plans = monitoringPlanRepository.getMonitoringPlansByOperationType(operationType).first()
                .filter { it.isEnabled }
                .sortedByDescending { it.priority }

            Log.i(TAG, "找到 ${plans.size} 个 $operationType 类型的启用监控计划")

            if (plans.isNotEmpty()) {
                monitoringTaskExecutor.executeBatchTasks(plans)
            } else {
                emptyList()
            }

        } catch (e: Exception) {
            Log.e(TAG, "执行指定类型监控计划时发生异常", e)
            emptyList()
        }
    }

    /**
     * 获取调度器状态
     * @return 调度器是否正在运行
     */
    fun isSchedulerRunning(): Boolean = isRunning

    /**
     * 手动触发一次调度检查（用于调试）
     */
    suspend fun triggerScheduleCheck() {
        Log.i(TAG, "手动触发调度检查")
        performScheduleCheck()
    }

    /**
     * 获取下次调度检查时间
     * @return 下次检查时间
     */
    fun getNextScheduleCheckTime(): Date {
        return Date(System.currentTimeMillis() + SCHEDULER_INTERVAL)
    }

    /**
     * 获取监控调度统计信息
     * @return 统计信息Map
     */
    suspend fun getSchedulerStatistics(): Map<String, Any?> {
        return try {
            val allPlans = monitoringPlanRepository.getAllMonitoringPlans().first()
            val enabledPlans = allPlans.filter { it.isEnabled }

            val intervalPlans = enabledPlans.filter { it.operationType == MonitoringOperationType.INTERVAL }
            val scheduledPlans = enabledPlans.filter { it.operationType == MonitoringOperationType.SCHEDULED }
            val manualPlans = enabledPlans.filter { it.operationType == MonitoringOperationType.MANUAL }

            val intervalToExecute = monitoringPlanRepository.getIntervalPlansToExecute()
            val scheduledToExecute = monitoringPlanRepository.getScheduledPlansToExecute()

            mapOf(
                "isRunning" to isRunning,
                "totalPlans" to allPlans.size,
                "enabledPlans" to enabledPlans.size,
                "intervalPlans" to intervalPlans.size,
                "scheduledPlans" to scheduledPlans.size,
                "manualPlans" to manualPlans.size,
                "intervalToExecute" to intervalToExecute.size,
                "scheduledToExecute" to scheduledToExecute.size,
                "nextCheckTime" to getNextScheduleCheckTime(),
                "schedulerInterval" to SCHEDULER_INTERVAL
            )

        } catch (e: Exception) {
            Log.e(TAG, "获取调度器统计信息时发生异常", e)
            mapOf(
                "isRunning" to isRunning,
                "error" to e.message
            )
        }
    }

    /**
     * 强制执行所有启用的监控计划
     * @return 执行结果列表
     */
    suspend fun forceExecuteAllEnabledPlans(): List<Pair<MonitoringPlanEntity, MonitoringTaskResult>> {
        Log.i(TAG, "强制执行所有启用的监控计划")

        return try {
            val enabledPlans = monitoringPlanRepository.getEnabledMonitoringPlans().first()
                .sortedByDescending { it.priority }

            Log.i(TAG, "找到 ${enabledPlans.size} 个启用的监控计划")

            if (enabledPlans.isNotEmpty()) {
                monitoringTaskExecutor.executeBatchTasks(enabledPlans)
            } else {
                emptyList()
            }

        } catch (e: Exception) {
            Log.e(TAG, "强制执行所有监控计划时发生异常", e)
            emptyList()
        }
    }
}