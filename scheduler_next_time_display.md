# 调度器下次执行时间显示功能

## 🎯 功能概述
为监控任务对话框添加调度器下次执行时间的显示功能，让用户能够清楚地看到调度器的状态和下次检查时间。

## ✨ 新增功能

### 1. 调度器状态显示
- **运行状态指示器**: 蓝色圆点表示运行中，红色圆点表示已停止
- **状态文本**: "调度器运行中" 或 "调度器已停止"
- **下次检查倒计时**: 实时显示距离下次检查的剩余时间

### 2. 可展开的详细信息
- **点击展开**: 点击调度器状态区域可展开/收起详细信息
- **检查间隔**: 显示调度器的检查间隔（60秒）
- **具体时间**: 显示下次检查的具体时间（HH:mm:ss格式）

### 3. 实时更新
- **状态同步**: 调度器启动/停止时状态立即更新
- **倒计时更新**: 每秒更新一次倒计时显示
- **事件驱动**: 监听调度器状态变化事件

## 🔧 技术实现

### 1. MonitoringSchedulerService 增强
```kotlin
// 添加执行时间跟踪
private var lastExecutionTime: Long = 0L

// 更精确的下次执行时间计算
fun getNextScheduleCheckTime(): Date? {
    return if (isRunning) {
        if (lastExecutionTime > 0) {
            Date(lastExecutionTime + SCHEDULER_INTERVAL)
        } else {
            Date(System.currentTimeMillis() + SCHEDULER_INTERVAL)
        }
    } else {
        null
    }
}

// 调度器详细状态
fun getSchedulerStatus(): Map<String, Any?> {
    return mapOf(
        "isRunning" to isRunning,
        "lastExecutionTime" to if (lastExecutionTime > 0) Date(lastExecutionTime) else null,
        "nextExecutionTime" to getNextScheduleCheckTime(),
        "intervalMs" to SCHEDULER_INTERVAL,
        "intervalSeconds" to SCHEDULER_INTERVAL / 1000
    )
}
```

### 2. UI组件更新
```kotlin
// 状态变量
var isSchedulerRunning by remember { mutableStateOf(false) }
var nextScheduleTime by remember { mutableStateOf<Date?>(null) }
var showSchedulerDetails by remember { mutableStateOf(false) }

// 实时更新逻辑
LaunchedEffect(isSchedulerRunning) {
    if (isSchedulerRunning) {
        while (true) {
            delay(1000) // 每秒更新一次
            nextScheduleTime = schedulerService.getNextScheduleCheckTime()
        }
    } else {
        nextScheduleTime = null
    }
}
```

### 3. 时间格式化工具
```kotlin
private fun formatRemainingTime(seconds: Long): String {
    return when {
        seconds <= 0 -> "即将执行"
        seconds < 60 -> "${seconds}秒"
        seconds < 3600 -> "${seconds / 60}分${seconds % 60}秒"
        else -> "${seconds / 3600}时${(seconds % 3600) / 60}分"
    }
}
```

## 🎨 UI设计

### 基本状态显示
```
🔵 调度器运行中 ▶
下次检查: 45秒
```

### 展开详细信息
```
🔵 调度器运行中 ▼
下次检查: 45秒

┌─────────────────────────┐
│ 调度器详情              │
│ 检查间隔: 60秒          │
│ 下次检查时间: 14:32:15  │
└─────────────────────────┘
```

### 停止状态
```
🔴 调度器已停止 ▶
```

## 📱 用户体验

### 1. 直观的状态指示
- **颜色编码**: 蓝色=运行中，红色=已停止
- **图标指示**: 圆点状态指示器
- **文字说明**: 清晰的状态描述

### 2. 实时信息更新
- **倒计时显示**: 实时显示剩余时间
- **自动刷新**: 无需手动刷新即可看到最新状态
- **即时响应**: 调度器状态变化立即反映在UI上

### 3. 详细信息按需显示
- **可展开设计**: 避免界面过于拥挤
- **点击交互**: 简单的点击即可查看详情
- **信息丰富**: 提供检查间隔和具体时间

## 🔍 使用场景

1. **监控调度状态**: 用户可以快速了解调度器是否正常运行
2. **预估执行时间**: 知道下次检查的具体时间，便于安排
3. **故障排查**: 当监控任务没有按预期执行时，可以检查调度器状态
4. **系统监控**: 管理员可以监控调度器的运行情况

## 📋 测试要点

1. **状态切换**: 启动/停止调度器，观察状态显示是否正确
2. **倒计时准确性**: 验证倒计时是否与实际执行时间一致
3. **展开/收起**: 测试详细信息的展开和收起功能
4. **时间格式**: 验证不同时间范围的格式化显示
5. **事件响应**: 确保调度器状态变化能及时反映在UI上

## 📁 相关文件

- `MonitoringSchedulerService.kt` - 调度器服务增强
- `MonitoringTaskDialog.kt` - UI显示组件
- `MonitoringEventBus.kt` - 事件通信机制
